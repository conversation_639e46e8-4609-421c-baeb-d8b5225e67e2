import { useState, useEffect } from "react";
import { conversationAPI } from "../../services/api";
import { authUtils } from "../../utils/auth";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUserPlus, 
  faUsers, 
  faUserFriends, 
  faSearch, 
  faPlus 
} from '@fortawesome/free-solid-svg-icons';
import socketService from "../../services/socket";
import CreateGroupModal from "./CreateGroupModal";
import SearchFriends from "../Friends/SearchFriends";
import FriendRequests from "../Friends/FriendRequests";
import FriendsList from "../Friends/FriendsList";
import "./ConversationList.css";

const ConversationList = ({
  onSelectConversation,
  selectedConversationId,
  showOnMobile = false,
}) => {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSearchFriends, setShowSearchFriends] = useState(false);
  const [showFriendRequests, setShowFriendRequests] = useState(false);
  const [showFriendsList, setShowFriendsList] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const currentUser = authUtils.getUser();

  useEffect(() => {
    fetchConversations();
  }, []);

  useEffect(() => {
    // Lắng nghe conversation cập nhật realtime
    const unsubscribe = socketService.onConversationUpdated(({ conversation, message }) => {
      if (!conversation) return;

      setConversations((prev) => {
        // Cập nhật hoặc thêm mới conversation
        const existsIndex = prev.findIndex((c) => c.id === conversation.id);
        let next = [];
        if (existsIndex !== -1) {
          next = [...prev];
          next[existsIndex] = conversation;
        } else {
          next = [conversation, ...prev];
        }

        // Sắp xếp: ưu tiên theo thời gian tin nhắn mới nhất
        next.sort((a, b) => {
          const aMsg = a.messages?.[0];
          const bMsg = b.messages?.[0];
          if (aMsg && bMsg) {
            return new Date(bMsg.createdAt) - new Date(aMsg.createdAt);
          }
          if (aMsg && !bMsg) return -1;
          if (!aMsg && bMsg) return 1;
          return new Date(b.createdAt) - new Date(a.createdAt);
        });

        return next;
      });
    });

    return () => {
      unsubscribe && unsubscribe();
    };
  }, []);

  const fetchConversations = async () => {
    try {
      setLoading(true);
      const response = await conversationAPI.getAll();
      const data = Array.isArray(response.data) ? response.data : [];

      // Join all conversations via Socket.IO
      const conversationIds = data.map((conv) => conv.id);
      if (conversationIds.length > 0) {
        socketService.joinConversations(conversationIds);
      }

      setConversations(data);
    } catch (err) {
      setError("Không thể tải danh sách cuộc trò chuyện");
      console.error("Error fetching conversations:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGroup = async (groupData) => {
    try {
      const response = await conversationAPI.create({
        name: groupData.name,
        isGroup: true,
      });

      // Thêm conversation mới vào đầu danh sách
      setConversations((prev) => [response.data, ...prev]);
      setShowCreateModal(false);

      // Auto select conversation mới tạo
      onSelectConversation(response.data);
    } catch (err) {
      console.error("Error creating group:", err);
      alert("Không thể tạo nhóm. Vui lòng thử lại.");
    }
  };

  const getConversationName = (conversation) => {
    if (conversation.isGroup) {
      return conversation.name || "Nhóm không tên";
    } else {
      // Với chat 1-1, hiển thị tên của user khác
      const otherMember = conversation.memberships?.find(
        (m) => m.user.id !== currentUser?.id
      );
      return otherMember?.user.username || "Người dùng";
    }
  };

  const getConversationAvatar = (conversation) => {
    if (conversation.isGroup) {
      return conversation.name?.charAt(0).toUpperCase() || "G";
    } else {
      // Với conversation 1-1, hiển thị avatar của người kia
      const otherMember = conversation.memberships?.find(
        (m) => m.user.id !== currentUser?.id
      );
      return otherMember?.user.username?.charAt(0).toUpperCase() || "U";
    }
  };

  const getLastMessage = (conversation) => {
    const lastMessage = conversation.messages?.[0];
    if (!lastMessage) return "Chưa có tin nhắn";

    return lastMessage.content.length > 50
      ? lastMessage.content.substring(0, 50) + "..."
      : lastMessage.content;
  };

  const getLastMessageTime = (conversation) => {
    const lastMessage = conversation.messages?.[0];
    if (!lastMessage) return "";

    const messageDate = new Date(lastMessage.createdAt);
    const now = new Date();
    const diffInHours = (now - messageDate) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString("vi-VN", {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else {
      return messageDate.toLocaleDateString("vi-VN", {
        day: "2-digit",
        month: "2-digit",
      });
    }
  };

  const filteredConversations = conversations.filter((conv) =>
    getConversationName(conv).toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="conversation-list">
        <div className="conversation-header">
          <h2>Tin nhắn</h2>
        </div>
        <div className="loading">Đang tải...</div>
      </div>
    );
  }

  return (
    <div className={`conversation-list ${showOnMobile ? "show-mobile" : ""}`}>
      <div className="conversation-header">
        <h2>Tin nhắn</h2>
        <div className="header-actions">
          <button
            className="action-btn"
            onClick={() => setShowFriendsList(true)}
            title="Danh sách bạn bè"
          >
            <FontAwesomeIcon icon={faUsers} />
          </button>

          <button
            className="action-btn"
            onClick={() => setShowFriendRequests(true)}
            title="Lời mời kết bạn"
          >
            <FontAwesomeIcon icon={faUserPlus} />
          </button>

          <button
            className="action-btn"
            onClick={() => setShowSearchFriends(true)}
            title="Tìm kiếm bạn bè"
          >
            <FontAwesomeIcon icon={faSearch} />
          </button>

          <button
            className="create-group-btn"
            onClick={() => setShowCreateModal(true)}
            title="Tạo nhóm mới"
          >
            <FontAwesomeIcon icon={faPlus} />
          </button>
        </div>
      </div>

      <div className="search-box">
        <FontAwesomeIcon icon={faSearch} className="search-icon" />
        <input
          type="text"
          placeholder="Tìm kiếm"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="conversations">
        {filteredConversations.length === 0 ? (
          <div className="no-conversations">
            <p>Chưa có cuộc trò chuyện nào</p>
            <button
              className="create-first-group"
              onClick={() => setShowCreateModal(true)}
            >
              Tạo nhóm đầu tiên
            </button>
          </div>
        ) : (
          filteredConversations.map((conversation) => (
            <div
              key={conversation.id}
              className={`conversation-item ${
                selectedConversationId === conversation.id ? "active" : ""
              }`}
              onClick={() => onSelectConversation(conversation)}
            >
              <div className="conversation-avatar">
                {conversation.isGroup ? (
                  <div className="group-avatar">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                    >
                      <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.002 3.002 0 0 0 17 6c-1.66 0-3 1.34-3 3 0 .35.07.69.18 1H12c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3h2c0-2.76-2.24-5-5-5S7 1.24 7 4s2.24 5 5 5h2.18c.11.31.18.65.18 1v2H9v6h2v4h2v-4h2v4h2v-4h3z" />
                    </svg>
                  </div>
                ) : (
                  <div className="user-avatar">
                    {getConversationAvatar(conversation)}
                  </div>
                )}
              </div>

              <div className="conversation-info">
                <div className="conversation-name">
                  {getConversationName(conversation)}
                </div>
                <div className="last-message">
                  {getLastMessage(conversation)}
                </div>
              </div>

              <div className="conversation-meta">
                <div className="last-time">
                  {getLastMessageTime(conversation)}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {showCreateModal && (
        <CreateGroupModal
          onClose={() => setShowCreateModal(false)}
          onCreateGroup={handleCreateGroup}
        />
      )}

      {showSearchFriends && (
        <SearchFriends onClose={() => setShowSearchFriends(false)} />
      )}

      {showFriendRequests && (
        <FriendRequests onClose={() => setShowFriendRequests(false)} />
      )}

      {showFriendsList && (
        <FriendsList
          onClose={() => setShowFriendsList(false)}
          onStartChat={(conversation) => {
            onSelectConversation(conversation);
            setShowFriendsList(false);
          }}
        />
      )}
    </div>
  );
};

export default ConversationList;
