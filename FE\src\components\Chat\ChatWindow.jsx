import { useState } from "react";
import { authUtils } from "../../utils/auth";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {  faPlus } from '@fortawesome/free-solid-svg-icons';
import AddMembersModal from "./AddMembersModal";
import MessageList from "./MessageList";
import MessageInput from "./MessageInput";
import "./ChatWindow.css";

const ChatWindow = ({ selectedConversation, onConversationUpdate }) => {
  const [showAddMembers, setShowAddMembers] = useState(false);
  const currentUser = authUtils.getUser();

  const getConversationName = (conversation) => {
    if (conversation.isGroup) {
      return conversation.name || "Nhóm không tên";
    } else {
      // Với chat 1-1, hiển thị tên của user khác
      const otherMember = conversation.memberships?.find(
        (m) => m.user.id !== currentUser?.id
      );
      return otherMember?.user.username || "Người dùng";
    }
  };

  const getConversationAvatar = (conversation) => {
    if (conversation.isGroup) {
      return conversation.name?.charAt(0).toUpperCase() || "G";
    } else {
      // Với conversation 1-1, hiển thị avatar của người kia
      const otherMember = conversation.memberships?.find(
        (m) => m.user.id !== currentUser?.id
      );
      return otherMember?.user.username?.charAt(0).toUpperCase() || "U";
    }
  };

  if (!selectedConversation) {
    return (
      <div className="chat-window">
        <div className="welcome-screen">
          <div className="welcome-content">
            <div className="welcome-icon">
              <svg
                width="80"
                height="80"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" />
              </svg>
            </div>
            <h2>Chào mừng đến với Chat Web!</h2>
            <p>Chọn một cuộc trò chuyện để bắt đầu nhắn tin</p>
            <div className="features">
              <div className="feature-item">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                </svg>
                <span>Tin nhắn real-time</span>
              </div>
              <div className="feature-item">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" />
                </svg>
                <span>Chat nhóm</span>
              </div>
              <div className="feature-item">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z" />
                </svg>
                <span>Lịch sử tin nhắn</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="chat-window">
      <div className="chat-header">
        <div className="chat-info">
          <div className="chat-avatar">
            {selectedConversation.isGroup ? (
              <div className="group-avatar">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.002 3.002 0 0 0 17 6c-1.66 0-3 1.34-3 3 0 .35.07.69.18 1H12c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3h2c0-2.76-2.24-5-5-5S7 1.24 7 4s2.24 5 5 5h2.18c.11.*********** 1v2H9v6h2v4h2v-4h2v4h2v-4h3z" />
                </svg>
              </div>
            ) : (
              <div className="user-avatar">
                {getConversationAvatar(selectedConversation)}
              </div>
            )}
          </div>
          <div className="chat-details">
            <h3>{getConversationName(selectedConversation)}</h3>
            <span className="member-count">
              {selectedConversation.isGroup 
                ? `${selectedConversation.memberships?.length || 0} thành viên`
                : "Trực tuyến"
              }
            </span>
          </div>
        </div>
        <div className="chat-actions">
          {selectedConversation.isGroup && (
            <button
              className="action-btn"
              onClick={() => setShowAddMembers(true)}
              title="Thêm thành viên"
            >
              <FontAwesomeIcon icon={faPlus} />
            </button>
          )}

          <button className="action-btn" title="Thông tin cuộc trò chuyện">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" />
            </svg>
          </button>
        </div>
      </div>

      <MessageList conversation={selectedConversation} />
      <MessageInput conversation={selectedConversation} />

      {showAddMembers && (
        <AddMembersModal
          conversation={selectedConversation}
          onClose={() => setShowAddMembers(false)}
          onMembersAdded={(addedMembers) => {
            // Callback để update conversation trong parent component
            if (onConversationUpdate) {
              onConversationUpdate(selectedConversation.id, addedMembers);
            }
            setShowAddMembers(false);
          }}
        />
      )}
    </div>
  );
};

export default ChatWindow;
