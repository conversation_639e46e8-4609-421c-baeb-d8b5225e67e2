.conversation-list {
  width: 320px;
  min-width: 320px;
  max-width: 320px;
  height: 100%;
  background: #fff;
  border-right: 1px solid #e4e6eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.conversation-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e6eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
}

.conversation-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #1c1e21;
  margin: 0;
  letter-spacing: -0.3px;
}

.header-actions {
  display: flex;
  gap: 6px;
}

.action-btn,
.create-group-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 14px;
}

.action-btn {
  background: #f0f2f5;
  color: #65676b;
}

.action-btn:hover {
  background: #e4e6eb;
  color: #1877f2;
}

.create-group-btn {
  background: #1877f2;
  color: white;
}

.create-group-btn:hover {
  background: #166fe5;
}

.search-box {
  padding: 12px 20px;
  border-bottom: 1px solid #e4e6eb;
  position: relative;
}

.search-box input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #e4e6eb;
  border-radius: 20px;
  font-size: 14px;
  background: #f0f2f5;
  outline: none;
  transition: all 0.2s ease;
  color: #1c1e21;
}

.search-box input:focus {
  border-color: #1877f2;
  background: white;
  box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.2);
}

.search-box input::placeholder {
  color: #65676b;
}

.search-icon {
  position: absolute;
  left: 32px;
  top: 50%;
  transform: translateY(-50%);
  color: #65676b;
  font-size: 14px;
}

.conversations {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  position: relative;
}

.conversation-item:hover {
  background: #f8f9fa;
}

.conversation-item.active {
  background: #e7f3ff;
  border-right: 3px solid #1877f2;
}

.conversation-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.user-avatar,
.group-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.group-avatar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-name {
  font-weight: 600;
  font-size: 15px;
  color: #1c1e21;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.last-message {
  font-size: 13px;
  color: #65676b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.conversation-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 8px;
}

.last-time {
  font-size: 12px;
  color: #65676b;
  margin-bottom: 4px;
  font-weight: 500;
}

.loading {
  padding: 40px 20px;
  text-align: center;
  color: #65676b;
}

.error-message {
  padding: 12px 20px;
  background: #fff2f0;
  color: #cf1322;
  border-bottom: 1px solid #ffccc7;
  font-size: 14px;
}

.no-conversations {
  padding: 40px 20px;
  text-align: center;
  color: #65676b;
}

.no-conversations p {
  margin-bottom: 16px;
  font-size: 15px;
}

.create-first-group {
  padding: 10px 20px;
  background: #1877f2;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.create-first-group:hover {
  background: #166fe5;
}

/* Scrollbar styling */
.conversations::-webkit-scrollbar {
  width: 6px;
}

.conversations::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.conversations::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.conversations::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mobile toggle styles */
.conversation-list.show-mobile {
  display: flex;
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .conversation-list {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100;
    height: 100%;
    display: none;
    background: #fff;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  }

  .conversation-list.show-mobile {
    display: flex;
  }

  .conversation-header {
    padding: 12px 16px;
  }

  .search-box {
    padding: 8px 16px;
  }

  .conversation-item {
    padding: 10px 16px;
  }
}

@media (max-width: 480px) {
  .conversation-list {
    width: 100vw;
    min-width: 100vw;
    max-width: 100vw;
  }

  .conversation-header {
    padding: 10px 12px;
  }

  .search-box {
    padding: 6px 12px;
  }

  .conversation-item {
    padding: 8px 12px;
  }
}
