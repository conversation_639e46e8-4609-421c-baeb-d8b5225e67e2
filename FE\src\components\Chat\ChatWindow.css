.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  height: 100%;
  min-width: 0;
  overflow: hidden;
}

.welcome-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f2f5 0%, #e4e6eb 100%);
  min-height: 0;
  overflow: hidden;
}

.welcome-content {
  text-align: center;
  max-width: 400px;
  padding: 40px;
  animation: fadeIn 0.5s ease-out;
}

.welcome-icon {
  margin-bottom: 24px;
  color: #1877f2;
  opacity: 0.8;
}

.welcome-content h2 {
  font-size: 28px;
  font-weight: 600;
  color: #1c1e21;
  margin-bottom: 12px;
  letter-spacing: -0.5px;
}

.welcome-content p {
  font-size: 16px;
  color: #65676b;
  margin-bottom: 32px;
  line-height: 1.5;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  font-size: 14px;
  color: #1c1e21;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-item svg {
  color: #1877f2;
}

.chat-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e6eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.chat-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-avatar .user-avatar,
.chat-avatar .group-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-avatar .user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.chat-avatar .group-avatar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.chat-details h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1c1e21;
}

.member-count {
  font-size: 12px;
  color: #65676b;
  font-weight: 500;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: #f0f2f5;
  color: #65676b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e4e6eb;
  color: #1877f2;
}

.messages-container {
  flex: 1;
  padding: 0px;
  overflow-y: auto;
  display: flex;
  
  justify-content: center;
  min-height: 0;
  background: #f0f2f5;
}

.no-messages {
  text-align: center;
  color: #65676b;
}

.no-messages p {
  margin: 8px 0;
  font-size: 14px;
}

.message-input-container {
  padding: 16px 20px;
  border-top: 1px solid #e4e6eb;
  background: #fff;
}

.message-input {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border: 1px solid #e4e6eb;
  border-radius: 24px;
  background: #f0f2f5;
  transition: all 0.2s ease;
}

.message-input:focus-within {
  border-color: #1877f2;
  background: white;
  box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.2);
}

.attach-btn, .send-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: none;
  color: #65676b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.attach-btn:hover {
  background: #e4e6eb;
  color: #1877f2;
}

.send-btn {
  background: #1877f2;
  color: white;
}

.send-btn:hover:not(:disabled) {
  background: #166fe5;
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.message-input input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  font-size: 14px;
  padding: 8px 0;
  color: #1c1e21;
}

.message-input input::placeholder {
  color: #65676b;
}

.message-input input:disabled {
  color: #ccc;
}

/* Responsive */
@media (max-width: 768px) {
  .chat-window {
    position: relative;
  }

  .welcome-content {
    padding: 20px;
    max-width: 300px;
  }

  .welcome-content h2 {
    font-size: 24px;
  }

  .welcome-content p {
    font-size: 14px;
  }

  .features {
    gap: 12px;
  }

  .feature-item {
    padding: 10px 16px;
    font-size: 13px;
  }

  .chat-header {
    padding: 0 16px;
  }

  .messages-container {
    padding: 0px;
  }
  
  .message-input-container {
    padding: 12px 16px;
  }

  .message-input {
    padding: 6px 10px;
  }

  .attach-btn, .send-btn {
    width: 28px;
    height: 28px;
  }
}

@media (max-width: 480px) {
  .welcome-content {
    padding: 16px;
    max-width: 280px;
  }

  .welcome-content h2 {
    font-size: 20px;
  }

  .welcome-content p {
    font-size: 13px;
  }

  .messages-container {
    padding: 0px;
  }

  .message-input-container {
    padding: 10px 12px;
  }
}
